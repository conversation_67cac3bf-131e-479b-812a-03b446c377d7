# User Management Service - Clean Architecture Design

## 1. Solution Structure

```
UserManagement.Solution/
├── src/
│   ├── Core/
│   │   ├── UserManagement.Domain/
│   │   │   ├── Entities/
│   │   │   │   ├── User.cs
│   │   │   │   ├── Role.cs
│   │   │   │   ├── Permission.cs
│   │   │   │   └── RolePermission.cs
│   │   │   ├── Enums/
│   │   │   │   ├── AccountStatus.cs
│   │   │   │   ├── Gender.cs
│   │   │   │   └── PermissionType.cs
│   │   │   ├── ValueObjects/
│   │   │   │   ├── Address.cs
│   │   │   │   └── ContactInfo.cs
│   │   │   ├── Events/
│   │   │   │   ├── UserCreatedEvent.cs
│   │   │   │   └── PasswordChangedEvent.cs
│   │   │   └── Interfaces/
│   │   │       └── IUserRepository.cs
│   │   └── UserManagement.Application/
│   │       ├── Common/
│   │       │   ├── Behaviors/
│   │       │   ├── Exceptions/
│   │       │   ├── Interfaces/
│   │       │   │   ├── IApplicationDbContext.cs
│   │       │   │   ├── IEmailService.cs
│   │       │   │   └── IPasswordGenerator.cs
│   │       │   └── Models/
│   │       ├── Features/
│   │       │   ├── Users/
│   │       │   │   ├── Commands/
│   │       │   │   │   ├── CreateUser/
│   │       │   │   │   ├── UpdateUser/
│   │       │   │   │   ├── ChangePassword/
│   │       │   │   │   └── ResetPassword/
│   │       │   │   └── Queries/
│   │       │   │       ├── GetUsers/
│   │       │   │       ├── GetUserById/
│   │       │   │       └── SearchUsers/
│   │       │   └── Roles/
│   │       │       ├── Commands/
│   │       │       │   ├── CreateRole/
│   │       │       │   └── UpdateRole/
│   │       │       └── Queries/
│   │       │           ├── GetRoles/
│   │       │           └── GetRoleById/
│   │       └── ConfigureServices.cs
│   ├── Infrastructure/
│   │   ├── UserManagement.Infrastructure/
│   │   │   ├── Data/
│   │   │   │   ├── ApplicationDbContext.cs
│   │   │   │   ├── Configurations/
│   │   │   │   └── Repositories/
│   │   │   ├── Services/
│   │   │   │   ├── EmailService.cs
│   │   │   │   ├── PasswordGenerator.cs
│   │   │   │   └── LocationService.cs
│   │   │   ├── Integration/
│   │   │   │   └── IdentityServer/
│   │   │   │       ├── IIdentityServerService.cs
│   │   │   │       └── IdentityServerService.cs
│   │   │   └── ConfigureServices.cs
│   │   └── UserManagement.Persistence/
│   │       ├── Migrations/
│   │       └── ConfigureServices.cs
│   └── Presentation/
│       └── UserManagement.API/
│           ├── Endpoints/
│           │   ├── UsersEndpoints.cs
│           │   └── RolesEndpoints.cs
│           ├── Middleware/
│           ├── Filters/
│           └── Program.cs
├── tests/
│   ├── UserManagement.UnitTests/
│   ├── UserManagement.IntegrationTests/
│   └── UserManagement.ArchitectureTests/
└── docker-compose.yml
```

## 2. Domain Layer Implementation

### 2.1 Core Entities

#### User Entity
```csharp
public class User : BaseAuditableEntity
{
    public string FullName { get; private set; }
    public string? PhoneNumber { get; private set; }
    public string Email { get; private set; }
    public Gender Gender { get; private set; }
    public Address Address { get; private set; }
    public string RoleId { get; private set; }
    public AccountStatus Status { get; private set; }
    public string PasswordHash { get; private set; }
    public bool IsFirstLogin { get; private set; }
    public DateTime? LastLoginDate { get; private set; }
    
    // Navigation properties
    public Role Role { get; private set; }
    
    // Domain events
    private readonly List<BaseEvent> _domainEvents = new();
    public IReadOnlyCollection<BaseEvent> DomainEvents => _domainEvents.AsReadOnly();
    
    public static User Create(string fullName, string email, Gender gender, 
        Address address, string roleId, string? phoneNumber = null)
    {
        var user = new User
        {
            Id = Guid.NewGuid().ToString(),
            FullName = fullName,
            Email = email,
            PhoneNumber = phoneNumber,
            Gender = gender,
            Address = address,
            RoleId = roleId,
            Status = AccountStatus.Active,
            IsFirstLogin = true,
            CreatedDate = DateTime.UtcNow
        };
        
        user.AddDomainEvent(new UserCreatedEvent(user));
        return user;
    }
    
    public void SetPassword(string passwordHash)
    {
        PasswordHash = passwordHash;
        if (IsFirstLogin)
        {
            IsFirstLogin = false;
        }
        AddDomainEvent(new PasswordChangedEvent(Id, Email));
    }
    
    public void UpdateProfile(string fullName, string? phoneNumber, 
        Gender gender, Address address)
    {
        FullName = fullName;
        PhoneNumber = phoneNumber;
        Gender = gender;
        Address = address;
        ModifiedDate = DateTime.UtcNow;
    }
    
    public void AssignRole(string roleId)
    {
        RoleId = roleId;
        ModifiedDate = DateTime.UtcNow;
    }
    
    public void Activate() => Status = AccountStatus.Active;
    public void Deactivate() => Status = AccountStatus.Inactive;
    
    private void AddDomainEvent(BaseEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
}
```

#### Role Entity
```csharp
public class Role : BaseAuditableEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public bool IsActive { get; private set; }
    
    // Navigation properties
    public ICollection<User> Users { get; private set; } = new List<User>();
    public ICollection<RolePermission> RolePermissions { get; private set; } = new List<RolePermission>();
    
    public static Role Create(string name, string description)
    {
        return new Role
        {
            Id = Guid.NewGuid().ToString(),
            Name = name,
            Description = description,
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };
    }
    
    public void Update(string name, string description)
    {
        Name = name;
        Description = description;
        ModifiedDate = DateTime.UtcNow;
    }
    
    public void AddPermission(string permissionId)
    {
        if (!RolePermissions.Any(rp => rp.PermissionId == permissionId))
        {
            RolePermissions.Add(new RolePermission(Id, permissionId));
        }
    }
    
    public void RemovePermission(string permissionId)
    {
        var rolePermission = RolePermissions.FirstOrDefault(rp => rp.PermissionId == permissionId);
        if (rolePermission != null)
        {
            RolePermissions.Remove(rolePermission);
        }
    }
}
```

### 2.2 Value Objects

#### Address Value Object
```csharp
public class Address : ValueObject
{
    public string Province { get; private set; }
    public string District { get; private set; }
    public string Ward { get; private set; }
    public string? DetailAddress { get; private set; }
    
    public Address(string province, string district, string ward, string? detailAddress = null)
    {
        Province = province;
        District = district;
        Ward = ward;
        DetailAddress = detailAddress;
    }
    
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Province;
        yield return District;
        yield return Ward;
        yield return DetailAddress ?? string.Empty;
    }
}
```

### 2.3 Enums

```csharp
public enum AccountStatus
{
    Active = 1,
    Inactive = 2,
    Locked = 3,
    Deleted = 4,
    Blocked = 5
}

public enum Gender
{
    Male = 1,
    Female = 2
}

public enum PermissionType
{
    UserManagement = 1,
    RoleManagement = 2,
    DocumentManagement = 3,
    AutoDetach = 4,
    ManualDetach = 5
}
```

## 3. Application Layer Implementation

### 3.1 CQRS Commands

#### Create User Command
```csharp
public class CreateUserCommand : IRequest<Result<CreateUserResponse>>
{
    public string FullName { get; set; }
    public string? PhoneNumber { get; set; }
    public string Email { get; set; }
    public Gender Gender { get; set; }
    public CreateAddressDto Address { get; set; }
    public string RoleId { get; set; }
    public AccountStatus Status { get; set; } = AccountStatus.Active;
}

public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, Result<CreateUserResponse>>
{
    private readonly IApplicationDbContext _context;
    private readonly IPasswordGenerator _passwordGenerator;
    private readonly IEmailService _emailService;
    private readonly IValidator<CreateUserCommand> _validator;
    
    public async Task<Result<CreateUserResponse>> Handle(CreateUserCommand request, 
        CancellationToken cancellationToken)
    {
        // Validation
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result<CreateUserResponse>.Failure(validationResult.Errors);
        }
        
        // Check duplicates
        if (await _context.Users.AnyAsync(u => u.Email == request.Email, cancellationToken))
        {
            return Result<CreateUserResponse>.Failure("Email already exists");
        }
        
        if (!string.IsNullOrEmpty(request.PhoneNumber) && 
            await _context.Users.AnyAsync(u => u.PhoneNumber == request.PhoneNumber, cancellationToken))
        {
            return Result<CreateUserResponse>.Failure("Phone number already exists");
        }
        
        // Create user
        var address = new Address(request.Address.Province, request.Address.District, 
            request.Address.Ward, request.Address.DetailAddress);
        
        var user = User.Create(request.FullName, request.Email, request.Gender, 
            address, request.RoleId, request.PhoneNumber);
        
        // Generate password
        var password = _passwordGenerator.GeneratePassword();
        var passwordHash = BCrypt.Net.BCrypt.HashPassword(password);
        user.SetPassword(passwordHash);
        
        _context.Users.Add(user);
        await _context.SaveChangesAsync(cancellationToken);
        
        return Result<CreateUserResponse>.Success(new CreateUserResponse
        {
            UserId = user.Id,
            Password = password
        });
    }
}
```

### 3.2 Queries

```csharp
public class UserCreatedEventHandler : INotificationHandler<UserCreatedEvent>
{
    private readonly IIdentityServerService _identityServerService;
    private readonly ILogger<UserCreatedEventHandler> _logger;
    
    public UserCreatedEventHandler(IIdentityServerService identityServerService, 
        ILogger<UserCreatedEventHandler> logger)
    {
        _identityServerService = identityServerService;
        _logger = logger;
    }
    
    public async Task Handle(UserCreatedEvent notification, CancellationToken cancellationToken)
    {
        var success = await _identityServerService.CreateUserAsync(
            notification.User.Id, 
            notification.User.Email, 
            notification.TempPassword);
            
        if (!success)
        {
            _logger.LogError("Failed to sync user {UserId} to IdentityServer", notification.User.Id);
            // You might want to implement retry logic or compensation actions
        }
    }
}

public class PasswordChangedEventHandler : INotificationHandler<PasswordChangedEvent>
{
    private readonly IIdentityServerService _identityServerService;
    private readonly ILogger<PasswordChangedEventHandler> _logger;
    
    public async Task Handle(PasswordChangedEvent notification, CancellationToken cancellationToken)
    {
        var success = await _identityServerService.ChangePasswordAsync(
            notification.UserId, 
            notification.NewPasswordHash);
            
        if (!success)
        {
            _logger.LogError("Failed to sync password change for user {UserId} to IdentityServer", 
                notification.UserId);
        }
    }
}
```

#### Get Users Query

```csharp
public class UserCreatedEventHandler : INotificationHandler<UserCreatedEvent>
{
    private readonly IIdentityServerService _identityServerService;
    private readonly ILogger<UserCreatedEventHandler> _logger;
    
    public UserCreatedEventHandler(IIdentityServerService identityServerService, 
        ILogger<UserCreatedEventHandler> logger)
    {
        _identityServerService = identityServerService;
        _logger = logger;
    }
    
    public async Task Handle(UserCreatedEvent notification, CancellationToken cancellationToken)
    {
        var success = await _identityServerService.CreateUserAsync(
            notification.User.Id, 
            notification.User.Email, 
            notification.TempPassword);
            
        if (!success)
        {
            _logger.LogError("Failed to sync user {UserId} to IdentityServer", notification.User.Id);
            // You might want to implement retry logic or compensation actions
        }
    }
}

public class PasswordChangedEventHandler : INotificationHandler<PasswordChangedEvent>
{
    private readonly IIdentityServerService _identityServerService;
    private readonly ILogger<PasswordChangedEventHandler> _logger;
    
    public async Task Handle(PasswordChangedEvent notification, CancellationToken cancellationToken)
    {
        var success = await _identityServerService.ChangePasswordAsync(
            notification.UserId, 
            notification.NewPasswordHash);
            
        if (!success)
        {
            _logger.LogError("Failed to sync password change for user {UserId} to IdentityServer", 
                notification.UserId);
        }
    }
}
```
```csharp
public class GetUsersQuery : IRequest<PaginatedList<UserDto>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public AccountStatus? Status { get; set; }
    public string? RoleId { get; set; }
}

public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, PaginatedList<UserDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;
    
    public async Task<PaginatedList<UserDto>> Handle(GetUsersQuery request, 
        CancellationToken cancellationToken)
    {
        var query = _context.Users
            .Include(u => u.Role)
            .AsQueryable();
        
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            query = query.Where(u => u.FullName.Contains(request.SearchTerm) ||
                                   u.Email.Contains(request.SearchTerm));
        }
        
        if (request.Status.HasValue)
        {
            query = query.Where(u => u.Status == request.Status);
        }
        
        if (!string.IsNullOrEmpty(request.RoleId))
        {
            query = query.Where(u => u.RoleId == request.RoleId);
        }
        
        query = query.OrderByDescending(u => u.CreatedDate);
        
        return await query.ProjectTo<UserDto>(_mapper.ConfigurationProvider)
            .PaginatedListAsync(request.PageNumber, request.PageSize);
    }
}
```

## 4. Infrastructure Layer Implementation

### 4.1 Database Context

```csharp
public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    private readonly IMediator _mediator;
    
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, 
        IMediator mediator) : base(options)
    {
        _mediator = mediator;
    }
    
    public DbSet<User> Users => Set<User>();
    public DbSet<Role> Roles => Set<Role>();
    public DbSet<Permission> Permissions => Set<Permission>();
    public DbSet<RolePermission> RolePermissions => Set<RolePermission>();
    
    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(builder);
    }
    
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        await _mediator.DispatchDomainEvents(this);
        return await base.SaveChangesAsync(cancellationToken);
    }
}
```

### 4.2 Entity Configurations

```csharp
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.HasKey(u => u.Id);
        
        builder.Property(u => u.FullName)
            .HasMaxLength(50)
            .IsRequired();
            
        builder.Property(u => u.Email)
            .HasMaxLength(54)
            .IsRequired();
            
        builder.HasIndex(u => u.Email)
            .IsUnique();
            
        builder.Property(u => u.PhoneNumber)
            .HasMaxLength(10);
            
        builder.HasIndex(u => u.PhoneNumber)
            .IsUnique()
            .HasFilter("[PhoneNumber] IS NOT NULL");
        
        builder.OwnsOne(u => u.Address, a =>
        {
            a.Property(p => p.Province).HasColumnName("Province").IsRequired();
            a.Property(p => p.District).HasColumnName("District").IsRequired();
            a.Property(p => p.Ward).HasColumnName("Ward").IsRequired();
            a.Property(p => p.DetailAddress).HasColumnName("DetailAddress");
        });
        
        builder.HasOne(u => u.Role)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RoleId);
    }
}
```

### 4.3 Integration with IdentityServer

**Note**: Duende IdentityServer does not provide built-in password management APIs. The following implementation simulates the integration you would build in your own IdentityServer instance or separate SSO service.

```csharp
public interface IIdentityServerService
{
    Task<bool> CreateUserAsync(string userId, string email, string password);
    Task<bool> UpdateUserAsync(string userId, string email);
    Task<bool> DeleteUserAsync(string userId);
    Task<bool> ChangePasswordAsync(string userId, string newPassword);
    Task<bool> ResetPasswordAsync(string userId, string newPassword);
    Task<bool> ValidateUserCredentialsAsync(string email, string password);
}

public class IdentityServerService : IIdentityServerService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IdentityServerService> _logger;
    
    public IdentityServerService(HttpClient httpClient, IConfiguration configuration, 
        ILogger<IdentityServerService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
    }
    
    public async Task<bool> CreateUserAsync(string userId, string email, string password)
    {
        try
        {
            // Simulated API call to your IdentityServer LocalAPI endpoint
            var request = new
            {
                UserId = userId,
                Email = email,
                Password = password,
                IsActive = true
            };
            
            var response = await _httpClient.PostAsJsonAsync("api/localapi/users", request);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("User {UserId} created successfully in IdentityServer", userId);
                return true;
            }
            
            _logger.LogWarning("Failed to create user {UserId} in IdentityServer. Status: {StatusCode}", 
                userId, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {UserId} in IdentityServer", userId);
            return false;
        }
    }
    
    public async Task<bool> ChangePasswordAsync(string userId, string newPassword)
    {
        try
        {
            // Simulated API call to your custom IdentityServer endpoint
            var request = new
            {
                UserId = userId,
                NewPassword = newPassword,
                RequirePasswordChangeOnNextLogin = false
            };
            
            var response = await _httpClient.PutAsJsonAsync($"api/localapi/users/{userId}/password", request);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Password changed successfully for user {UserId}", userId);
                return true;
            }
            
            _logger.LogWarning("Failed to change password for user {UserId}. Status: {StatusCode}", 
                userId, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user {UserId}", userId);
            return false;
        }
    }
    
    public async Task<bool> ResetPasswordAsync(string userId, string newPassword)
    {
        try
        {
            // Simulated password reset API call
            var request = new
            {
                UserId = userId,
                NewPassword = newPassword,
                RequirePasswordChangeOnNextLogin = true
            };
            
            var response = await _httpClient.PostAsJsonAsync($"api/localapi/users/{userId}/reset-password", request);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Password reset successfully for user {UserId}", userId);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password for user {UserId}", userId);
            return false;
        }
    }
    
    public async Task<bool> UpdateUserAsync(string userId, string email)
    {
        try
        {
            var request = new
            {
                UserId = userId,
                Email = email
            };
            
            var response = await _httpClient.PutAsJsonAsync($"api/localapi/users/{userId}", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId} in IdentityServer", userId);
            return false;
        }
    }
    
    public async Task<bool> DeleteUserAsync(string userId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"api/localapi/users/{userId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId} from IdentityServer", userId);
            return false;
        }
    }
    
    public async Task<bool> ValidateUserCredentialsAsync(string email, string password)
    {
        try
        {
            // This would typically use Resource Owner Password Credentials Grant
            // But since ROPC is deprecated, you might implement a custom validation endpoint
            var request = new
            {
                Email = email,
                Password = password
            };
            
            var response = await _httpClient.PostAsJsonAsync("api/localapi/validate-credentials", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating credentials for user {Email}", email);
            return false;
        }
    }
}

// Configuration for HttpClient with Bearer token authentication
public class IdentityServerServiceConfiguration
{
    public static void ConfigureHttpClient(IServiceCollection services, IConfiguration configuration)
    {
        services.AddHttpClient<IIdentityServerService, IdentityServerService>(client =>
        {
            client.BaseAddress = new Uri(configuration["IdentityServer:BaseUrl"]);
            client.DefaultRequestHeaders.Add("Accept", "application/json");
        })
        .AddHttpMessageHandler<IdentityServerAuthenticationHandler>();
    }
}

// Message handler to add authentication token
public class IdentityServerAuthenticationHandler : DelegatingHandler
{
    private readonly IAccessTokenManagement _accessTokenManagement;
    
    public IdentityServerAuthenticationHandler(IAccessTokenManagement accessTokenManagement)
    {
        _accessTokenManagement = accessTokenManagement;
    }
    
    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Get client credentials token for machine-to-machine communication
        var token = await _accessTokenManagement.GetClientAccessTokenAsync("identityserver_api");
        
        if (!string.IsNullOrEmpty(token))
        {
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        }
        
        return await base.SendAsync(request, cancellationToken);
    }
}
```

## 5. API Layer Implementation

### 5.1 Users Endpoints

```csharp
public static class UsersEndpoints
{
    public static IEndpointRouteBuilder MapUsersEndpoints(this IEndpointRouteBuilder endpoints)
    {
        var group = endpoints.MapGroup("/api/users")
            .WithTags("Users")
            .RequireAuthorization();

        group.MapPost("/", CreateUser)
            .WithName("CreateUser")
            .WithOpenApi()
            .RequirePermission(PermissionType.UserManagement);

        group.MapGet("/", GetUsers)
            .WithName("GetUsers")
            .WithOpenApi()
            .RequirePermission(PermissionType.UserManagement);

        group.MapGet("/{id}", GetUserById)
            .WithName("GetUserById")
            .WithOpenApi()
            .RequirePermission(PermissionType.UserManagement);

        group.MapPut("/{id}", UpdateUser)
            .WithName("UpdateUser")
            .WithOpenApi()
            .RequirePermission(PermissionType.UserManagement);

        group.MapPost("/{id}/change-password", ChangePassword)
            .WithName("ChangePassword")
            .WithOpenApi()
            .RequirePermission(PermissionType.UserManagement);

        group.MapPost("/{id}/reset-password", ResetPassword)
            .WithName("ResetPassword")
            .WithOpenApi()
            .RequirePermission(PermissionType.UserManagement);

        return endpoints;
    }

    private static async Task<IResult> CreateUser(
        CreateUserCommand command, 
        IMediator mediator)
    {
        var result = await mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Results.Ok(result.Value);
        }
        
        return Results.BadRequest(result.Errors);
    }

    private static async Task<IResult> GetUsers(
        [AsParameters] GetUsersQuery query,
        IMediator mediator)
    {
        var result = await mediator.Send(query);
        return Results.Ok(result);
    }

    private static async Task<IResult> GetUserById(
        string id,
        IMediator mediator)
    {
        var query = new GetUserByIdQuery { Id = id };
        var result = await mediator.Send(query);
        
        if (result == null)
        {
            return Results.NotFound();
        }
        
        return Results.Ok(result);
    }

    private static async Task<IResult> UpdateUser(
        string id,
        UpdateUserCommand command,
        IMediator mediator)
    {
        if (id != command.Id)
        {
            return Results.BadRequest("ID mismatch");
        }
        
        var result = await mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Results.NoContent();
        }
        
        return Results.BadRequest(result.Errors);
    }

    private static async Task<IResult> ChangePassword(
        string id,
        ChangePasswordCommand command,
        IMediator mediator)
    {
        command.UserId = id;
        var result = await mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Results.Ok();
        }
        
        return Results.BadRequest(result.Errors);
    }

    private static async Task<IResult> ResetPassword(
        string id,
        ResetPasswordCommand command,
        IMediator mediator)
    {
        command.UserId = id;
        var result = await mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Results.Ok();
        }
        
        return Results.BadRequest(result.Errors);
    }
}
```

### 5.2 Roles Endpoints

```csharp
public static class RolesEndpoints
{
    public static IEndpointRouteBuilder MapRolesEndpoints(this IEndpointRouteBuilder endpoints)
    {
        var group = endpoints.MapGroup("/api/roles")
            .WithTags("Roles")
            .RequireAuthorization();

        group.MapPost("/", CreateRole)
            .WithName("CreateRole")
            .WithOpenApi()
            .RequirePermission(PermissionType.RoleManagement);

        group.MapGet("/", GetRoles)
            .WithName("GetRoles")
            .WithOpenApi()
            .RequirePermission(PermissionType.RoleManagement);

        group.MapGet("/{id}", GetRoleById)
            .WithName("GetRoleById")
            .WithOpenApi()
            .RequirePermission(PermissionType.RoleManagement);

        group.MapPut("/{id}", UpdateRole)
            .WithName("UpdateRole")
            .WithOpenApi()
            .RequirePermission(PermissionType.RoleManagement);

        group.MapPost("/{id}/permissions", AssignPermissions)
            .WithName("AssignPermissions")
            .WithOpenApi()
            .RequirePermission(PermissionType.RoleManagement);

        return endpoints;
    }

    private static async Task<IResult> CreateRole(
        CreateRoleCommand command,
        IMediator mediator)
    {
        var result = await mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Results.Created($"/api/roles/{result.Value.RoleId}", result.Value);
        }
        
        return Results.BadRequest(result.Errors);
    }

    private static async Task<IResult> GetRoles(
        [AsParameters] GetRolesQuery query,
        IMediator mediator)
    {
        var result = await mediator.Send(query);
        return Results.Ok(result);
    }

    private static async Task<IResult> GetRoleById(
        string id,
        IMediator mediator)
    {
        var query = new GetRoleByIdQuery { Id = id };
        var result = await mediator.Send(query);
        
        if (result == null)
        {
            return Results.NotFound();
        }
        
        return Results.Ok(result);
    }

    private static async Task<IResult> UpdateRole(
        string id,
        UpdateRoleCommand command,
        IMediator mediator)
    {
        if (id != command.Id)
        {
            return Results.BadRequest("ID mismatch");
        }
        
        var result = await mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Results.NoContent();
        }
        
        return Results.BadRequest(result.Errors);
    }

    private static async Task<IResult> AssignPermissions(
        string id,
        AssignPermissionsCommand command,
        IMediator mediator)
    {
        command.RoleId = id;
        var result = await mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Results.Ok();
        }
        
        return Results.BadRequest(result.Errors);
    }
}
```

### 5.3 Permission Extensions

```csharp
public static class EndpointExtensions
{
    public static RouteHandlerBuilder RequirePermission(
        this RouteHandlerBuilder builder, 
        PermissionType permission)
    {
        return builder.RequireAuthorization(policy =>
            policy.RequireClaim("permission", permission.ToString()));
    }
}
```

## 6. Configuration & Startup

```csharp
// Add this to your Application layer ConfigureServices
public static class ConfigureServices
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        services.AddAutoMapper(Assembly.GetExecutingAssembly());
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
        services.AddMediatR(cfg => {
            cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
        });
        
        // Register domain event handlers
        services.AddScoped<INotificationHandler<UserCreatedEvent>, UserCreatedEventHandler>();
        services.AddScoped<INotificationHandler<PasswordChangedEvent>, PasswordChangedEventHandler>();
        
        return services;
    }
}

// Infrastructure layer configuration for IdentityServer integration
public static class InfrastructureServices
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, 
        IConfiguration configuration)
    {
        // Database
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));
        
        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());
        
        // Services
        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<IPasswordGenerator, PasswordGenerator>();
        services.AddScoped<ILocationService, LocationService>();
        
        // IdentityServer Integration
        IdentityServerServiceConfiguration.ConfigureHttpClient(services, configuration);
        
        // Authentication for accessing IdentityServer APIs
        services.AddAccessTokenManagement(options =>
        {
            options.Client.Clients.Add("identityserver_api", new ClientCredentialsTokenRequest
            {
                Address = configuration["IdentityServer:TokenEndpoint"],
                ClientId = configuration["IdentityServer:ClientId"],
                ClientSecret = configuration["IdentityServer:ClientSecret"],
                Scope = "identityserver_localapi"
            });
        });
        
        services.AddTransient<IdentityServerAuthenticationHandler>();
        
        return services;
    }
}
```

### 6.3 IdentityServer LocalAPI Implementation (Reference)

**Note**: This is what you would implement in your IdentityServer project to support the User Management service calls.

```csharp
// In your IdentityServer project
[Route("api/localapi/[controller]")]
[Authorize(LocalApi.PolicyName)]
[ApiController]
public class UsersController : ControllerBase
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<UsersController> _logger;
    
    public UsersController(UserManager<ApplicationUser> userManager, ILogger<UsersController> logger)
    {
        _userManager = userManager;
        _logger = logger;
    }
    
    [HttpPost]
    public async Task<IActionResult> CreateUser([FromBody] CreateIdentityUserRequest request)
    {
        var user = new ApplicationUser
        {
            Id = request.UserId,
            UserName = request.Email,
            Email = request.Email,
            EmailConfirmed = true
        };
        
        var result = await _userManager.CreateAsync(user, request.Password);
        
        if (result.Succeeded)
        {
            return Ok();
        }
        
        return BadRequest(result.Errors);
    }
    
    [HttpPut("{userId}/password")]
    public async Task<IActionResult> ChangePassword(string userId, [FromBody] ChangePasswordRequest request)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            return NotFound();
        }
        
        // Remove old password and set new one
        await _userManager.RemovePasswordAsync(user);
        var result = await _userManager.AddPasswordAsync(user, request.NewPassword);
        
        if (result.Succeeded)
        {
            return Ok();
        }
        
        return BadRequest(result.Errors);
    }
    
    [HttpPost("{userId}/reset-password")]
    public async Task<IActionResult> ResetPassword(string userId, [FromBody] ResetPasswordRequest request)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            return NotFound();
        }
        
        var token = await _userManager.GeneratePasswordResetTokenAsync(user);
        var result = await _userManager.ResetPasswordAsync(user, token, request.NewPassword);
        
        if (result.Succeeded)
        {
            return Ok();
        }
        
        return BadRequest(result.Errors);
    }
    
    [HttpDelete("{userId}")]
    public async Task<IActionResult> DeleteUser(string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            return NotFound();
        }
        
        var result = await _userManager.DeleteAsync(user);
        return result.Succeeded ? Ok() : BadRequest(result.Errors);
    }
    
    [HttpPost("validate-credentials")]
    public async Task<IActionResult> ValidateCredentials([FromBody] ValidateCredentialsRequest request)
    {
        var user = await _userManager.FindByEmailAsync(request.Email);
        if (user == null)
        {
            return Unauthorized();
        }
        
        var isValid = await _userManager.CheckPasswordAsync(user, request.Password);
        return isValid ? Ok() : Unauthorized();
    }
}

// DTOs for IdentityServer API
public class CreateIdentityUserRequest
{
    public string UserId { get; set; }
    public string Email { get; set; }
    public string Password { get; set; }
}

public class ChangePasswordRequest
{
    public string NewPassword { get; set; }
    public bool RequirePasswordChangeOnNextLogin { get; set; }
}

public class ResetPasswordRequest
{
    public string NewPassword { get; set; }
    public bool RequirePasswordChangeOnNextLogin { get; set; } = true;
}

public class ValidateCredentialsRequest
{
    public string Email { get; set; }
    public string Password { get; set; }
}
```

### 6.1 Program.cs

```csharp
var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddApplicationServices();
builder.Services.AddInfrastructureServices(builder.Configuration);
builder.Services.AddPersistenceServices(builder.Configuration);

// Add Authentication
builder.Services.AddAuthentication("Bearer")
    .AddJwtBearer("Bearer", options =>
    {
        options.Authority = builder.Configuration["IdentityServer:Authority"];
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateAudience = false
        };
    });

// Add Authorization with permission policies
builder.Services.AddAuthorization(options =>
{
    foreach (var permission in Enum.GetValues<PermissionType>())
    {
        options.AddPolicy(permission.ToString(), policy =>
            policy.RequireClaim("permission", permission.ToString()));
    }
});

// Add API services
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add problem details support
builder.Services.AddProblemDetails();

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler();
}

app.UseStatusCodePages();

app.UseAuthentication();
app.UseAuthorization();

// Map minimal API endpoints
app.MapUsersEndpoints();
app.MapRolesEndpoints();

// Health check endpoint
app.MapGet("/health", () => Results.Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow }))
    .WithName("HealthCheck")
    .WithTags("Health");

app.Run();
```

### 6.2 Additional Query Parameter Binding

For complex query parameters with minimal APIs, you can use the `[AsParameters]` attribute:

```csharp
public class GetUsersQuery
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public AccountStatus? Status { get; set; }
    public string? RoleId { get; set; }
    
    // Implement implicit operators for query binding
    public static ValueTask<GetUsersQuery?> BindAsync(ParameterInfo parameter, object? value)
    {
        // Custom binding logic if needed
        return ValueTask.FromResult(value as GetUsersQuery);
    }
}
```

### 6.3 OpenAPI Configuration

```csharp
// In ConfigureServices extension
public static IServiceCollection AddSwaggerServices(this IServiceCollection services)
{
    services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo 
        { 
            Title = "User Management API", 
            Version = "v1",
            Description = "Clean Architecture User Management Service with minimal APIs"
        });

        c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });

        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                Array.Empty<string>()
            }
        });
    });

    return services;
}
```

## 7. Key Design Decisions

### 7.1 Separation of Concerns
- **User Management Service**: Handles user profile, roles, permissions
- **Identity Server**: Handles authentication, tokens, SSO
- Clear boundaries via well-defined contracts

### 7.2 Integration Strategy
- Use HTTP APIs for communication with IdentityServer
- Maintain user correlation via shared UserId
- Event-driven synchronization for critical data

### 7.3 Security
- JWT Bearer token authentication
- Permission-based authorization
- Secure password generation and hashing

### 7.4 Data Consistency
- Domain events for cross-service communication
- Eventual consistency for non-critical data
- Strong consistency for user identity data

### 7.5 Minimal API Benefits
- **Performance**: Reduced overhead compared to controller-based APIs
- **Simplicity**: Less ceremony and boilerplate code
- **Testability**: Easier to unit test individual endpoint handlers
- **Organization**: Clear separation of endpoint logic in dedicated classes
- **OpenAPI Integration**: Built-in support for OpenAPI documentation
- **Dependency Injection**: Native support for DI in endpoint handlers
- **Source Generators**: Better performance through compile-time optimizations

### 7.6 Migration Considerations
- **From Controllers**: Easy migration path - endpoints can coexist with controllers
- **Authorization**: Policy-based authorization works seamlessly
- **Model Binding**: Support for complex parameter binding including `[AsParameters]`
- **Filters**: Global filters can be applied to endpoint groups
- **Middleware**: All existing middleware continues to work

This architecture provides a clean separation between authentication (IdentityServer) and user management while leveraging modern minimal API patterns for better performance and maintainability. The structure scales well as your OCR document processing requirements evolve.