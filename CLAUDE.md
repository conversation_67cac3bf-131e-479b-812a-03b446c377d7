# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Architecture

This is a **User Management Service** following **Clean Architecture** principles with:
- **Domain Layer**: Core business entities (User, Role, Permission) with rich domain models
- **Application Layer**: CQRS pattern using MediatR for commands/queries
- **Infrastructure Layer**: Database access and external service integrations
- **Presentation Layer**: Minimal APIs for endpoints

### Key Architecture Patterns
- **Domain-Driven Design**: Rich domain entities with business logic encapsulation
- **CQRS**: Command Query Responsibility Segregation with MediatR
- **Event Sourcing**: Domain events for cross-service communication
- **Clean Architecture**: Dependency inversion with interfaces at core layers

## Project Structure (Planned)

Based on the planning document, the solution will be organized as:

```
UserManagement.Solution/
├── src/
│   ├── Core/
│   │   ├── UserManagement.Domain/     # Entities, ValueObjects, Events
│   │   └── UserManagement.Application/ # Commands, Queries, Handlers
│   ├── Infrastructure/
│   │   ├── UserManagement.Infrastructure/ # Services, Integrations
│   │   └── UserManagement.Persistence/    # Database, Repositories
│   └── Presentation/
│       └── UserManagement.API/        # Minimal API endpoints
└── tests/                             # Unit, Integration, Architecture tests
```

## Development Commands

**Note**: This appears to be a planning phase project. No build files (*.csproj, *.sln, docker-compose.yml) exist yet.

Once implemented, typical .NET commands would be:
- `dotnet build` - Build the solution
- `dotnet test` - Run all tests
- `dotnet run --project src/Presentation/UserManagement.API` - Start the API
- `dotnet ef migrations add <name>` - Add EF Core migrations
- `dotnet ef database update` - Apply migrations

## Key Domain Concepts

### Core Entities
- **User**: Central entity with profile info, authentication data, role assignment
- **Role**: Permission containers with role-based access control
- **Permission**: Granular access rights (UserManagement, RoleManagement, etc.)
- **Address**: Value object for location data

### Domain Events
- **UserCreatedEvent**: Triggers IdentityServer synchronization
- **PasswordChangedEvent**: Synchronizes password changes with IdentityServer

### Business Rules
- Email and phone number uniqueness validation
- Password auto-generation for new users
- First-time login tracking
- Account status management (Active, Inactive, Locked, etc.)

## External Integrations

### IdentityServer Integration
- User sync via HTTP APIs to IdentityServer LocalAPI
- Client credentials authentication for service-to-service calls
- Separate concerns: UserService handles profiles, IdentityServer handles authentication

### Authentication & Authorization
- JWT Bearer token authentication
- Permission-based authorization using claims
- Role-permission mapping for fine-grained access control

## Technology Stack (Planned)
- **.NET 8** with Minimal APIs
- **Entity Framework Core** for data persistence
- **MediatR** for CQRS implementation
- **AutoMapper** for object mapping
- **FluentValidation** for input validation
- **BCrypt.Net** for password hashing
- **SQL Server** for primary database
- **Duende IdentityServer** integration for SSO

## Development Guidelines

### Code Patterns
- Use factory methods for entity creation (e.g., `User.Create()`)
- Implement domain events for cross-boundary communication
- Follow CQRS with separate command/query handlers
- Use value objects for complex data types (Address)
- Apply validation behaviors in MediatR pipeline

### API Design
- Minimal APIs with endpoint grouping
- Permission-based endpoint authorization
- OpenAPI documentation generation
- Result pattern for error handling
- Consistent HTTP status codes

### Testing Strategy
- Unit tests for domain logic
- Integration tests for API endpoints
- Architecture tests for dependency validation