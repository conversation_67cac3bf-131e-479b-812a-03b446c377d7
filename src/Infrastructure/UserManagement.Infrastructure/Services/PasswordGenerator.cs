using System.Security.Cryptography;
using System.Text;
using UserManagement.Application.Common.Interfaces;

namespace UserManagement.Infrastructure.Services;

public class PasswordGenerator : IPasswordGenerator
{
    private const string LowerCase = "abcdefghijklmnopqrstuvwxyz";
    private const string UpperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private const string Numbers = "0123456789";
    private const string SpecialChars = "!@#$%^&*";

    public string GeneratePassword(int length = 12)
    {
        if (length < 8)
            throw new ArgumentException("Password length must be at least 8 characters");

        var allChars = LowerCase + UpperCase + Numbers + SpecialChars;
        var password = new StringBuilder();

        // Ensure at least one character from each category
        password.Append(GetRandomChar(LowerCase));
        password.Append(GetRandomChar(UpperCase));
        password.Append(GetRandomChar(Numbers));
        password.Append(GetRandomChar(SpecialChars));

        // Fill the rest randomly
        for (int i = 4; i < length; i++)
        {
            password.Append(GetRandomChar(allChars));
        }

        // Shuffle the password
        return ShuffleString(password.ToString());
    }

    public bool ValidatePasswordStrength(string password)
    {
        if (string.IsNullOrEmpty(password) || password.Length < 8)
            return false;

        bool hasLower = password.Any(char.IsLower);
        bool hasUpper = password.Any(char.IsUpper);
        bool hasDigit = password.Any(char.IsDigit);
        bool hasSpecial = password.Any(c => SpecialChars.Contains(c));

        return hasLower && hasUpper && hasDigit && hasSpecial;
    }

    private static char GetRandomChar(string chars)
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var randomIndex = Math.Abs(BitConverter.ToInt32(bytes, 0)) % chars.Length;
        return chars[randomIndex];
    }

    private static string ShuffleString(string input)
    {
        var array = input.ToCharArray();
        using var rng = RandomNumberGenerator.Create();
        
        for (int i = array.Length - 1; i > 0; i--)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var j = Math.Abs(BitConverter.ToInt32(bytes, 0)) % (i + 1);
            (array[i], array[j]) = (array[j], array[i]);
        }
        
        return new string(array);
    }
}
