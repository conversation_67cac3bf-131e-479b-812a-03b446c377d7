using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UserManagement.Domain.Entities;

namespace UserManagement.Infrastructure.Data.Configurations;

public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.HasKey(u => u.Id);

        builder.Property(u => u.FullName)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(u => u.Email)
            .HasMaxLength(54)
            .IsRequired();

        builder.HasIndex(u => u.Email)
            .IsUnique();

        builder.Property(u => u.PhoneNumber)
            .HasMaxLength(10);

        builder.HasIndex(u => u.PhoneNumber)
            .IsUnique()
            .HasFilter("[PhoneNumber] IS NOT NULL");

        builder.Property(u => u.PasswordHash)
            .IsRequired();

        builder.Property(u => u.Gender)
            .HasConversion<int>();

        builder.Property(u => u.Status)
            .HasConversion<int>();

        builder.OwnsOne(u => u.Address, a =>
        {
            a.Property(p => p.Province).HasColumnName("Province").IsRequired();
            a.Property(p => p.District).HasColumnName("District").IsRequired();
            a.Property(p => p.Ward).HasColumnName("Ward").IsRequired();
            a.Property(p => p.DetailAddress).HasColumnName("DetailAddress");
        });

        builder.HasOne(u => u.Role)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RoleId)
            .OnDelete(DeleteBehavior.Restrict);

        // Ignore domain events for EF Core
        builder.Ignore(u => u.DomainEvents);
    }
}
