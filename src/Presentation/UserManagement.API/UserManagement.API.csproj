<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Core\UserManagement.Application\UserManagement.Application.csproj" />
    <ProjectReference Include="..\..\Infrastructure\UserManagement.Infrastructure\UserManagement.Infrastructure.csproj" />
    <ProjectReference Include="..\..\Infrastructure\UserManagement.Persistence\UserManagement.Persistence.csproj" />
  </ItemGroup>

</Project>
