using MediatR;
using Microsoft.AspNetCore.Mvc;
using UserManagement.Application.Features.Users.Commands.CreateUser;
using UserManagement.Application.Features.Users.Queries.GetUsers;

namespace UserManagement.API.Endpoints;

public static class UsersEndpoints
{
    public static IEndpointRouteBuilder MapUsersEndpoints(this IEndpointRouteBuilder endpoints)
    {
        var group = endpoints.MapGroup("/api/users")
            .WithTags("Users")
            .WithOpenApi();

        group.MapPost("/", CreateUser)
            .WithName("CreateUser")
            .WithSummary("Create a new user")
            .WithDescription("Creates a new user with the provided information and generates a temporary password");

        group.MapGet("/", GetUsers)
            .WithName("GetUsers")
            .WithSummary("Get users with pagination")
            .WithDescription("Retrieves a paginated list of users with optional filtering");

        return endpoints;
    }

    private static async Task<IResult> CreateUser(
        CreateUserCommand command,
        IMediator mediator)
    {
        var result = await mediator.Send(command);

        if (result.IsSuccess)
        {
            return Results.Created($"/api/users/{result.Value!.UserId}", result.Value);
        }

        return Results.BadRequest(new { Errors = result.Errors });
    }

    private static async Task<IResult> GetUsers(
        [AsParameters] GetUsersQuery query,
        IMediator mediator)
    {
        var result = await mediator.Send(query);
        return Results.Ok(result);
    }
}
