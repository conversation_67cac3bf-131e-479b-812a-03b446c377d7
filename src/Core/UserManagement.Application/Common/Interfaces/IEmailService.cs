namespace UserManagement.Application.Common.Interfaces;

public interface IEmailService
{
    Task SendWelcomeEmailAsync(string email, string fullName, string tempPassword, CancellationToken cancellationToken = default);
    Task SendPasswordResetEmailAsync(string email, string fullName, string newPassword, CancellationToken cancellationToken = default);
    Task SendPasswordChangedNotificationAsync(string email, string fullName, CancellationToken cancellationToken = default);
}
