{"format": 1, "restore": {"/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Application/UserManagement.Application.csproj": {}}, "projects": {"/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Application/UserManagement.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Application/UserManagement.Application.csproj", "projectName": "UserManagement.Application", "projectPath": "/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Application/UserManagement.Application.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Domain/UserManagement.Domain.csproj": {"projectPath": "/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Domain/UserManagement.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "FluentValidation": {"target": "Package", "version": "[12.0.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[12.0.0, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Domain/UserManagement.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Domain/UserManagement.Domain.csproj", "projectName": "UserManagement.Domain", "projectPath": "/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Domain/UserManagement.Domain.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Volumes/DATA/DigitalSolution.UserService/src/Core/UserManagement.Domain/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MediatR": {"target": "Package", "version": "[12.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}