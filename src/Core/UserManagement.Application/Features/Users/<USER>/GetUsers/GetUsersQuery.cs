using MediatR;
using UserManagement.Application.Common.Models;
using UserManagement.Domain.Enums;

namespace UserManagement.Application.Features.Users.Queries.GetUsers;

public class GetUsersQuery : IRequest<PaginatedList<UserDto>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public AccountStatus? Status { get; set; }
    public string? RoleId { get; set; }
}
