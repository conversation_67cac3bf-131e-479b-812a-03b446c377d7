using MediatR;
using Microsoft.EntityFrameworkCore;
using UserManagement.Application.Common.Interfaces;
using UserManagement.Application.Common.Models;

namespace UserManagement.Application.Features.Users.Queries.GetUsers;

public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, PaginatedList<UserDto>>
{
    private readonly IApplicationDbContext _context;

    public GetUsersQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<PaginatedList<UserDto>> <PERSON>le(GetUsersQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Users
            .Include(u => u.Role)
            .AsQueryable();

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            query = query.Where(u => u.FullName.Contains(request.SearchTerm) ||
                                   u.Email.Contains(request.SearchTerm));
        }

        if (request.Status.HasValue)
        {
            query = query.Where(u => u.Status == request.Status);
        }

        if (!string.IsNullOrEmpty(request.RoleId))
        {
            query = query.Where(u => u.RoleId == request.RoleId);
        }

        query = query.OrderByDescending(u => u.CreatedDate);

        var totalCount = await query.CountAsync(cancellationToken);
        var users = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(u => new UserDto
            {
                Id = u.Id,
                FullName = u.FullName,
                PhoneNumber = u.PhoneNumber,
                Email = u.Email,
                Gender = u.Gender,
                Address = new AddressDto
                {
                    Province = u.Address.Province,
                    District = u.Address.District,
                    Ward = u.Address.Ward,
                    DetailAddress = u.Address.DetailAddress
                },
                RoleId = u.RoleId,
                RoleName = u.Role.Name,
                Status = u.Status,
                IsFirstLogin = u.IsFirstLogin,
                LastLoginDate = u.LastLoginDate,
                CreatedDate = u.CreatedDate,
                ModifiedDate = u.ModifiedDate
            })
            .ToListAsync(cancellationToken);

        return new PaginatedList<UserDto>(users, totalCount, request.PageNumber, request.PageSize);
    }
}
