using MediatR;
using UserManagement.Application.Common.Models;
using UserManagement.Domain.Enums;

namespace UserManagement.Application.Features.Users.Commands.CreateUser;

public class CreateUserCommand : IRequest<Result<CreateUserResponse>>
{
    public string FullName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string Email { get; set; } = string.Empty;
    public Gender Gender { get; set; }
    public CreateAddressDto Address { get; set; } = null!;
    public string RoleId { get; set; } = string.Empty;
    public AccountStatus Status { get; set; } = AccountStatus.Active;
}

public class CreateAddressDto
{
    public string Province { get; set; } = string.Empty;
    public string District { get; set; } = string.Empty;
    public string Ward { get; set; } = string.Empty;
    public string? DetailAddress { get; set; }
}

public class CreateUserResponse
{
    public string UserId { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}
