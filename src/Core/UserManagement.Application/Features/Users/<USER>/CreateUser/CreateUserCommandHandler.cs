using MediatR;
using Microsoft.EntityFrameworkCore;
using UserManagement.Application.Common.Interfaces;
using UserManagement.Application.Common.Models;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Events;
using UserManagement.Domain.ValueObjects;

namespace UserManagement.Application.Features.Users.Commands.CreateUser;

public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, Result<CreateUserResponse>>
{
    private readonly IApplicationDbContext _context;
    private readonly IPasswordGenerator _passwordGenerator;
    private readonly IMediator _mediator;

    public CreateUserCommandHandler(
        IApplicationDbContext context,
        IPasswordGenerator passwordGenerator,
        IMediator mediator)
    {
        _context = context;
        _passwordGenerator = passwordGenerator;
        _mediator = mediator;
    }

    public async Task<Result<CreateUserResponse>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Check if email already exists
        if (await _context.Users.AnyAsync(u => u.Email == request.Email, cancellationToken))
        {
            return Result<CreateUserResponse>.Failure("Email already exists");
        }

        // Check if phone number already exists (if provided)
        if (!string.IsNullOrEmpty(request.PhoneNumber) &&
            await _context.Users.AnyAsync(u => u.PhoneNumber == request.PhoneNumber, cancellationToken))
        {
            return Result<CreateUserResponse>.Failure("Phone number already exists");
        }

        // Check if role exists
        if (!await _context.Roles.AnyAsync(r => r.Id == request.RoleId && r.IsActive, cancellationToken))
        {
            return Result<CreateUserResponse>.Failure("Role not found or inactive");
        }

        // Create address value object
        var address = new Address(
            request.Address.Province,
            request.Address.District,
            request.Address.Ward,
            request.Address.DetailAddress);

        // Create user
        var user = User.Create(
            request.FullName,
            request.Email,
            request.Gender,
            address,
            request.RoleId,
            request.PhoneNumber);

        // Generate password
        var tempPassword = _passwordGenerator.GeneratePassword();
        var passwordHash = BCrypt.Net.BCrypt.HashPassword(tempPassword);
        user.SetPassword(passwordHash, tempPassword);

        // Add domain event
        user.AddDomainEvent(new UserCreatedEvent(user, tempPassword));

        _context.Users.Add(user);
        await _context.SaveChangesAsync(cancellationToken);

        return Result<CreateUserResponse>.Success(new CreateUserResponse
        {
            UserId = user.Id,
            Password = tempPassword
        });
    }
}
