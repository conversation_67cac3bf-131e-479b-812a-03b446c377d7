using FluentValidation;

namespace UserManagement.Application.Features.Users.Commands.CreateUser;

public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    public CreateUserCommandValidator()
    {
        RuleFor(x => x.FullName)
            .NotEmpty().WithMessage("Full name is required")
            .MaximumLength(50).WithMessage("Full name must not exceed 50 characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Email must be a valid email address")
            .MaximumLength(54).WithMessage("Email must not exceed 54 characters");

        RuleFor(x => x.PhoneNumber)
            .Matches(@"^\d{10}$").WithMessage("Phone number must be exactly 10 digits")
            .When(x => !string.IsNullOrEmpty(x.PhoneNumber));

        RuleFor(x => x.Gender)
            .IsInEnum().WithMessage("Gender must be a valid value");

        RuleFor(x => x.RoleId)
            .NotEmpty().WithMessage("Role is required");

        RuleFor(x => x.Address)
            .NotNull().WithMessage("Address is required");

        RuleFor(x => x.Address.Province)
            .NotEmpty().WithMessage("Province is required")
            .When(x => x.Address != null);

        RuleFor(x => x.Address.District)
            .NotEmpty().WithMessage("District is required")
            .When(x => x.Address != null);

        RuleFor(x => x.Address.Ward)
            .NotEmpty().WithMessage("Ward is required")
            .When(x => x.Address != null);
    }
}
