using UserManagement.Domain.Common;

namespace UserManagement.Domain.ValueObjects;

public class Address : ValueObject
{
    public string Province { get; private set; }
    public string District { get; private set; }
    public string Ward { get; private set; }
    public string? DetailAddress { get; private set; }

    public Address(string province, string district, string ward, string? detailAddress = null)
    {
        Province = province;
        District = district;
        Ward = ward;
        DetailAddress = detailAddress;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Province;
        yield return District;
        yield return Ward;
        yield return DetailAddress ?? string.Empty;
    }

    public override string ToString()
    {
        var parts = new List<string> { Ward, District, Province };
        if (!string.IsNullOrEmpty(DetailAddress))
        {
            parts.Insert(0, DetailAddress);
        }
        return string.Join(", ", parts);
    }
}
