using UserManagement.Domain.Common;

namespace UserManagement.Domain.Events;

public class PasswordChangedEvent : BaseEvent
{
    public string UserId { get; }
    public string Email { get; }
    public string NewPasswordHash { get; }

    public PasswordChangedEvent(string userId, string email, string newPasswordHash)
    {
        UserId = userId;
        Email = email;
        NewPasswordHash = newPasswordHash;
    }
}
