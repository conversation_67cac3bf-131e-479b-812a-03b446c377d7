using UserManagement.Domain.Common;

namespace UserManagement.Domain.Entities;

public class RolePermission : BaseEntity
{
    public string RoleId { get; private set; } = string.Empty;
    public string PermissionId { get; private set; } = string.Empty;

    // Navigation properties
    public Role Role { get; private set; } = null!;
    public Permission Permission { get; private set; } = null!;

    private RolePermission() { } // For EF Core

    public RolePermission(string roleId, string permissionId)
    {
        Id = Guid.NewGuid().ToString();
        RoleId = roleId;
        PermissionId = permissionId;
    }
}
