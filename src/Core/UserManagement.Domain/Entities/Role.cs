using UserManagement.Domain.Common;

namespace UserManagement.Domain.Entities;

public class Role : BaseAuditableEntity
{
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public bool IsActive { get; private set; }

    // Navigation properties
    public ICollection<User> Users { get; private set; } = new List<User>();
    public ICollection<RolePermission> RolePermissions { get; private set; } = new List<RolePermission>();

    private Role() { } // For EF Core

    public static Role Create(string name, string description)
    {
        return new Role
        {
            Id = Guid.NewGuid().ToString(),
            Name = name,
            Description = description,
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };
    }

    public void Update(string name, string description)
    {
        Name = name;
        Description = description;
        ModifiedDate = DateTime.UtcNow;
    }

    public void AddPermission(string permissionId)
    {
        if (!RolePermissions.Any(rp => rp.PermissionId == permissionId))
        {
            RolePermissions.Add(new RolePermission(Id, permissionId));
        }
    }

    public void RemovePermission(string permissionId)
    {
        var rolePermission = RolePermissions.FirstOrDefault(rp => rp.PermissionId == permissionId);
        if (rolePermission != null)
        {
            RolePermissions.Remove(rolePermission);
        }
    }

    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;
}
