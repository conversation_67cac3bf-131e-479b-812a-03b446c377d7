using UserManagement.Domain.Common;
using UserManagement.Domain.Enums;
using UserManagement.Domain.Events;
using UserManagement.Domain.ValueObjects;

namespace UserManagement.Domain.Entities;

public class User : BaseAuditableEntity
{
    public string FullName { get; private set; } = string.Empty;
    public string? PhoneNumber { get; private set; }
    public string Email { get; private set; } = string.Empty;
    public Gender Gender { get; private set; }
    public Address Address { get; private set; } = null!;
    public string RoleId { get; private set; } = string.Empty;
    public AccountStatus Status { get; private set; }
    public string PasswordHash { get; private set; } = string.Empty;
    public bool IsFirstLogin { get; private set; }
    public DateTime? LastLoginDate { get; private set; }

    // Navigation properties
    public Role Role { get; private set; } = null!;

    // Domain events
    private readonly List<BaseEvent> _domainEvents = new();
    public IReadOnlyCollection<BaseEvent> DomainEvents => _domainEvents.AsReadOnly();

    private User() { } // For EF Core

    public static User Create(string fullName, string email, Gender gender,
        Address address, string roleId, string? phoneNumber = null)
    {
        var user = new User
        {
            Id = Guid.NewGuid().ToString(),
            FullName = fullName,
            Email = email,
            PhoneNumber = phoneNumber,
            Gender = gender,
            Address = address,
            RoleId = roleId,
            Status = AccountStatus.Active,
            IsFirstLogin = true,
            CreatedDate = DateTime.UtcNow
        };

        return user;
    }

    public void SetPassword(string passwordHash, string tempPassword)
    {
        PasswordHash = passwordHash;
        if (IsFirstLogin)
        {
            IsFirstLogin = false;
        }
        AddDomainEvent(new PasswordChangedEvent(Id, Email, passwordHash));
    }

    public void UpdateProfile(string fullName, string? phoneNumber,
        Gender gender, Address address)
    {
        FullName = fullName;
        PhoneNumber = phoneNumber;
        Gender = gender;
        Address = address;
        ModifiedDate = DateTime.UtcNow;
    }

    public void AssignRole(string roleId)
    {
        RoleId = roleId;
        ModifiedDate = DateTime.UtcNow;
    }

    public void UpdateLastLogin()
    {
        LastLoginDate = DateTime.UtcNow;
    }

    public void Activate() => Status = AccountStatus.Active;
    public void Deactivate() => Status = AccountStatus.Inactive;
    public void Lock() => Status = AccountStatus.Locked;
    public void Block() => Status = AccountStatus.Blocked;

    public void AddDomainEvent(BaseEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}
