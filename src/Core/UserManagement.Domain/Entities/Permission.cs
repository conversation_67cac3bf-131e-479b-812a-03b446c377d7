using UserManagement.Domain.Common;
using UserManagement.Domain.Enums;

namespace UserManagement.Domain.Entities;

public class Permission : BaseAuditableEntity
{
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public PermissionType Type { get; private set; }
    public bool IsActive { get; private set; }

    // Navigation properties
    public ICollection<RolePermission> RolePermissions { get; private set; } = new List<RolePermission>();

    private Permission() { } // For EF Core

    public static Permission Create(string name, string description, PermissionType type)
    {
        return new Permission
        {
            Id = Guid.NewGuid().ToString(),
            Name = name,
            Description = description,
            Type = type,
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };
    }

    public void Update(string name, string description)
    {
        Name = name;
        Description = description;
        ModifiedDate = DateTime.UtcNow;
    }

    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;
}
