﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{8D626EA8-CB54-BC41-363A-217881BEBA6E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.Domain", "src\Core\UserManagement.Domain\UserManagement.Domain.csproj", "{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.Application", "src\Core\UserManagement.Application\UserManagement.Application.csproj", "{147E25CC-993D-4078-9E60-C0201766E0C6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{9048EB7F-3875-A59E-E36B-5BD4C6F2A282}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.Infrastructure", "src\Infrastructure\UserManagement.Infrastructure\UserManagement.Infrastructure.csproj", "{68E506D6-31FC-43B1-ADFE-56D41A058598}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.Persistence", "src\Infrastructure\UserManagement.Persistence\UserManagement.Persistence.csproj", "{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation", "Presentation", "{6996BBF7-DB31-59BC-C7DD-9A7CCC6138C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.API", "src\Presentation\UserManagement.API\UserManagement.API.csproj", "{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.UnitTests", "tests\UserManagement.UnitTests\UserManagement.UnitTests.csproj", "{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.IntegrationTests", "tests\UserManagement.IntegrationTests\UserManagement.IntegrationTests.csproj", "{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.ArchitectureTests", "tests\UserManagement.ArchitectureTests\UserManagement.ArchitectureTests.csproj", "{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Debug|x64.Build.0 = Debug|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Debug|x86.Build.0 = Debug|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Release|x64.ActiveCfg = Release|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Release|x64.Build.0 = Release|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Release|x86.ActiveCfg = Release|Any CPU
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A}.Release|x86.Build.0 = Release|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Debug|x64.Build.0 = Debug|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Debug|x86.Build.0 = Debug|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Release|Any CPU.Build.0 = Release|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Release|x64.ActiveCfg = Release|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Release|x64.Build.0 = Release|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Release|x86.ActiveCfg = Release|Any CPU
		{147E25CC-993D-4078-9E60-C0201766E0C6}.Release|x86.Build.0 = Release|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Debug|x64.ActiveCfg = Debug|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Debug|x64.Build.0 = Debug|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Debug|x86.ActiveCfg = Debug|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Debug|x86.Build.0 = Debug|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Release|Any CPU.Build.0 = Release|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Release|x64.ActiveCfg = Release|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Release|x64.Build.0 = Release|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Release|x86.ActiveCfg = Release|Any CPU
		{68E506D6-31FC-43B1-ADFE-56D41A058598}.Release|x86.Build.0 = Release|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Debug|x64.Build.0 = Debug|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Debug|x86.Build.0 = Debug|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Release|x64.ActiveCfg = Release|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Release|x64.Build.0 = Release|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Release|x86.ActiveCfg = Release|Any CPU
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F}.Release|x86.Build.0 = Release|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Debug|x64.Build.0 = Debug|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Debug|x86.Build.0 = Debug|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Release|x64.ActiveCfg = Release|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Release|x64.Build.0 = Release|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Release|x86.ActiveCfg = Release|Any CPU
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2}.Release|x86.Build.0 = Release|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Debug|x64.Build.0 = Debug|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Debug|x86.Build.0 = Debug|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Release|Any CPU.Build.0 = Release|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Release|x64.ActiveCfg = Release|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Release|x64.Build.0 = Release|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Release|x86.ActiveCfg = Release|Any CPU
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A}.Release|x86.Build.0 = Release|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Debug|x64.Build.0 = Debug|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Debug|x86.Build.0 = Debug|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Release|Any CPU.Build.0 = Release|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Release|x64.ActiveCfg = Release|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Release|x64.Build.0 = Release|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Release|x86.ActiveCfg = Release|Any CPU
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC}.Release|x86.Build.0 = Release|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Debug|x64.Build.0 = Debug|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Debug|x86.Build.0 = Debug|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Release|Any CPU.Build.0 = Release|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Release|x64.ActiveCfg = Release|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Release|x64.Build.0 = Release|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Release|x86.ActiveCfg = Release|Any CPU
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8D626EA8-CB54-BC41-363A-217881BEBA6E} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{8BD0A305-26EE-4894-904B-4D3B0AE80B4A} = {8D626EA8-CB54-BC41-363A-217881BEBA6E}
		{147E25CC-993D-4078-9E60-C0201766E0C6} = {8D626EA8-CB54-BC41-363A-217881BEBA6E}
		{9048EB7F-3875-A59E-E36B-5BD4C6F2A282} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{68E506D6-31FC-43B1-ADFE-56D41A058598} = {9048EB7F-3875-A59E-E36B-5BD4C6F2A282}
		{EF7AAB70-37AF-4B34-98A0-FACDAB42222F} = {9048EB7F-3875-A59E-E36B-5BD4C6F2A282}
		{6996BBF7-DB31-59BC-C7DD-9A7CCC6138C4} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{801BB173-9F19-4DFC-BD03-FFF1BDACAAC2} = {6996BBF7-DB31-59BC-C7DD-9A7CCC6138C4}
		{155FDD39-A2C1-4F08-BBB2-B9053E9C858A} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{61A9A196-9B9F-4F66-9037-1EAAA93BCBCC} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{122D07B6-51DA-48E3-BE5C-4A6F284B1BC9} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
EndGlobal
