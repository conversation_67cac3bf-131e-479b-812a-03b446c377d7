{"format": 1, "restore": {"/Volumes/DATA/DigitalSolution.UserService/tests/UserManagement.IntegrationTests/UserManagement.IntegrationTests.csproj": {}}, "projects": {"/Volumes/DATA/DigitalSolution.UserService/tests/UserManagement.IntegrationTests/UserManagement.IntegrationTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Volumes/DATA/DigitalSolution.UserService/tests/UserManagement.IntegrationTests/UserManagement.IntegrationTests.csproj", "projectName": "UserManagement.IntegrationTests", "projectPath": "/Volumes/DATA/DigitalSolution.UserService/tests/UserManagement.IntegrationTests/UserManagement.IntegrationTests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Volumes/DATA/DigitalSolution.UserService/tests/UserManagement.IntegrationTests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}